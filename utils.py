#!/usr/bin/env python3
"""
ScrapeTenderly Utilities
Common functions and constants used throughout the application
"""

import re
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from bs4 import BeautifulSoup
import time

# Target keywords for filtering tenders
TARGET_KEYWORDS = [
    'Empanelment', 'Architect', 'Interior', 'Interiors', 'Furnishing',
    'Consultant', 'Consultants', 'Consultancy', 'Contractor', 'Construction',
    'enlistment', 'enlist', 'Interior design', 'Interior decoration'
]

def setup_chrome_driver():
    """Set up Chrome driver with optimized options"""
    chrome_options = Options()
    chrome_options.add_argument('--headless')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')
    chrome_options.add_argument('--disable-extensions')
    chrome_options.add_argument('--disable-plugins')
    chrome_options.add_argument('--disable-images')
    chrome_options.add_argument('--disable-javascript')
    
    try:
        driver = webdriver.Chrome(options=chrome_options)
        driver.set_page_load_timeout(30)
        return driver
    except Exception as e:
        print(f"❌ Error setting up Chrome driver: {e}")
        return None

def contains_target_keywords(text):
    """Check if text contains any target keywords"""
    if not text:
        return False
    
    text_lower = text.lower()
    return any(keyword.lower() in text_lower for keyword in TARGET_KEYWORDS)

def filter_tenders_by_keywords(tenders):
    """Filter tenders that contain target keywords"""
    if not tenders:
        return []

    filtered = []
    for tender in tenders:
        title = tender.get('title', '')
        description = tender.get('description', '')

        if contains_target_keywords(title) or contains_target_keywords(description):
            filtered.append(tender)

    return filtered

def create_tender_object(title, source_name, source_url, reference_number='',
                        closing_date='', bid_opening_date='', published_date='',
                        tender_link='', document_link='', authority_name='',
                        description=''):
    """Create a standardized tender object dictionary"""
    return {
        'title': title or '',
        'reference_number': reference_number or '',
        'closing_date': closing_date or '',
        'bid_opening_date': bid_opening_date or '',
        'published_date': published_date or '',
        'tender_link': tender_link or '',
        'document_link': document_link or '',
        'source_url': source_url or '',
        'source_name': source_name or '',
        'authority_name': authority_name or source_name or '',
        'description': description or title or ''
    }

def highlight_keywords_in_text(text):
    """Highlight target keywords in text with HTML spans"""
    if not text:
        return text
    
    highlighted_text = text
    for keyword in TARGET_KEYWORDS:
        pattern = re.compile(re.escape(keyword), re.IGNORECASE)
        highlighted_text = pattern.sub(
            f'<span class="keyword-highlight" style="background-color: #007bff; color: white; padding: 2px 4px; border-radius: 3px; font-weight: bold;">{keyword}</span>',
            highlighted_text
        )
    
    return highlighted_text

def scrape_banking_site(driver, site_config, max_pages=1, delay=1):
    """Scrape banking tender sites"""
    tenders = []
    
    try:
        print(f"🏦 Scraping banking site: {site_config['name']}")
        driver.get(site_config['url'])
        time.sleep(delay)
        
        # Wait for page to load
        WebDriverWait(driver, 10).wait(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        # Get page source and parse with BeautifulSoup
        soup = BeautifulSoup(driver.page_source, 'html.parser')
        
        # Generic tender extraction
        tender_elements = soup.find_all(['tr', 'div', 'li'], class_=lambda x: x and any(
            keyword in str(x).lower() for keyword in ['tender', 'notice', 'bid', 'rfp', 'quotation']
        ))
        
        for element in tender_elements[:20]:  # Limit to 20 tenders
            title_elem = element.find(['a', 'span', 'td', 'div'], string=lambda text: text and len(text.strip()) > 10)
            
            if title_elem:
                title = title_elem.get_text(strip=True)
                
                if len(title) > 20 and contains_target_keywords(title):
                    tender_link = ''
                    if title_elem.name == 'a' and title_elem.get('href'):
                        tender_link = title_elem['href']
                        if tender_link.startswith('/'):
                            tender_link = site_config['url'].rstrip('/') + tender_link
                    
                    tender = {
                        'title': title,
                        'reference_number': '',
                        'closing_date': '',
                        'bid_opening_date': '',
                        'published_date': '',
                        'tender_link': tender_link,
                        'document_link': '',
                        'source_url': site_config['url'],
                        'source_name': site_config['name'],
                        'authority_name': site_config['name'],
                        'description': title[:200] + '...' if len(title) > 200 else title
                    }
                    
                    tenders.append(tender)
        
        print(f"✅ Found {len(tenders)} relevant tenders from {site_config['name']}")
        
    except Exception as e:
        print(f"❌ Error scraping {site_config['name']}: {str(e)}")
    
    return tenders

def scrape_generic_site(driver, site_config, max_pages=1, delay=1):
    """Scrape generic tender sites"""
    tenders = []
    
    try:
        print(f"🌐 Scraping generic site: {site_config['name']}")
        driver.get(site_config['url'])
        time.sleep(delay)
        
        # Wait for page to load
        WebDriverWait(driver, 10).wait(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        # Get page source and parse with BeautifulSoup
        soup = BeautifulSoup(driver.page_source, 'html.parser')
        
        # Look for tender-related content
        tender_elements = soup.find_all(['a', 'div', 'tr', 'li'], string=lambda text: text and any(
            keyword.lower() in text.lower() for keyword in TARGET_KEYWORDS
        ))
        
        for element in tender_elements[:15]:  # Limit to 15 tenders
            title = element.get_text(strip=True)
            
            if len(title) > 15:
                tender_link = ''
                if element.name == 'a' and element.get('href'):
                    tender_link = element['href']
                    if tender_link.startswith('/'):
                        tender_link = site_config['url'].rstrip('/') + tender_link
                
                tender = {
                    'title': title,
                    'reference_number': '',
                    'closing_date': '',
                    'bid_opening_date': '',
                    'published_date': '',
                    'tender_link': tender_link,
                    'document_link': '',
                    'source_url': site_config['url'],
                    'source_name': site_config['name'],
                    'authority_name': site_config['name'],
                    'description': title[:200] + '...' if len(title) > 200 else title
                }
                
                tenders.append(tender)
        
        print(f"✅ Found {len(tenders)} relevant tenders from {site_config['name']}")
        
    except Exception as e:
        print(f"❌ Error scraping {site_config['name']}: {str(e)}")
    
    return tenders

class GovernmentPortalScraper:
    """Specialized scraper for government portals"""
    
    def __init__(self, driver):
        self.driver = driver
    
    def scrape_gem_portal(self, site_config):
        """Scrape GeM portal specifically"""
        tenders = []
        
        try:
            print(f"💎 Scraping GeM portal: {site_config['name']}")
            self.driver.get(site_config['url'])
            time.sleep(2)
            
            # Wait for page to load
            WebDriverWait(self.driver, 15).wait(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Get page source and parse
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            # Look for tender/bid related content
            tender_elements = soup.find_all(['div', 'tr', 'a'], string=lambda text: text and any(
                keyword.lower() in text.lower() for keyword in TARGET_KEYWORDS + ['tender', 'bid', 'rfp']
            ))
            
            for element in tender_elements[:25]:  # Limit to 25 tenders
                title = element.get_text(strip=True)
                
                if len(title) > 20 and contains_target_keywords(title):
                    tender_link = ''
                    if element.name == 'a' and element.get('href'):
                        tender_link = element['href']
                        if tender_link.startswith('/'):
                            tender_link = 'https://gem.gov.in' + tender_link
                    
                    tender = {
                        'title': title,
                        'reference_number': '',
                        'closing_date': '',
                        'bid_opening_date': '',
                        'published_date': '',
                        'tender_link': tender_link,
                        'document_link': '',
                        'source_url': site_config['url'],
                        'source_name': site_config['name'],
                        'authority_name': 'Government e-Marketplace (GeM)',
                        'description': title[:200] + '...' if len(title) > 200 else title
                    }
                    
                    tenders.append(tender)
            
            print(f"✅ Found {len(tenders)} relevant tenders from GeM portal")
            
        except Exception as e:
            print(f"❌ Error scraping GeM portal: {str(e)}")
        
        return tenders
