/* Portal Preview Styles */

/* Portal Preview Modal */
#portalPreviewModal .modal-dialog {
    margin: 0;
    max-width: 100%;
    height: 100vh;
}

#portalPreviewModal .modal-content {
    height: 100vh;
    border-radius: 0;
}

#portalPreviewModal .modal-header {
    border-bottom: 2px solid #dee2e6;
    padding: 0.75rem 1rem;
    background: linear-gradient(135deg, #343a40 0%, #495057 100%);
}

#portalPreviewModal .modal-footer {
    border-top: 2px solid #dee2e6;
    padding: 0.5rem 1rem;
    background-color: #f8f9fa;
}

/* Portal Preview Frame */
#portalPreviewFrame {
    transition: opacity 0.3s ease;
}

#portalPreviewFrame.loading {
    opacity: 0.5;
}

/* Loading Overlay */
#portalLoadingOverlay {
    background: rgba(248, 249, 250, 0.95);
    backdrop-filter: blur(2px);
}

#portalLoadingOverlay .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Error State */
#portalErrorState {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* URL Bar Styling */
#portalUrlBar {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
}

#portalUrlBar:focus {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
    color: white;
}

#portalUrlBar::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

/* Navigation Buttons */
.modal-header .btn-outline-light {
    border-color: rgba(255, 255, 255, 0.3);
    color: rgba(255, 255, 255, 0.9);
}

.modal-header .btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
}

.modal-header .btn-outline-light:active {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.6);
}

/* Portal Card Preview Button */
.portal-card .btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    border: none;
    transition: all 0.3s ease;
}

.portal-card .btn-info:hover {
    background: linear-gradient(135deg, #138496 0%, #117a8b 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(23, 162, 184, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    #portalPreviewModal .modal-header {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    #portalPreviewModal .modal-header .d-flex {
        width: 100%;
    }
    
    #portalPreviewModal .input-group {
        max-width: 100%;
    }
    
    #portalPreviewFrame {
        height: 70vh;
    }
}

/* Animation for smooth transitions */
.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out;
}

.modal.show .modal-dialog {
    transform: none;
}

/* Custom scrollbar for iframe (webkit browsers) */
#portalPreviewFrame::-webkit-scrollbar {
    width: 8px;
}

#portalPreviewFrame::-webkit-scrollbar-track {
    background: #f1f1f1;
}

#portalPreviewFrame::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

#portalPreviewFrame::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Portal Preview Enhancements */
.portal-preview-controls {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 0.375rem;
    padding: 0.25rem;
}

/* Status indicators */
.portal-status-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1001;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
}

/* Fullscreen mode adjustments */
.modal-fullscreen .modal-body {
    overflow: hidden;
}

.modal-fullscreen iframe {
    border: none;
    outline: none;
}

/* Loading animation */
@keyframes portalLoadPulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
}

.portal-loading {
    animation: portalLoadPulse 1.5s ease-in-out infinite;
}
