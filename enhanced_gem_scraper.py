#!/usr/bin/env python3
"""
Enhanced GeM Portal Scraper
- Implements pagination navigation
- Searches for specific keywords like 'empanelment'
- Extracts comprehensive tender data from all pages
"""

import time
import re
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from utils import contains_target_keywords, create_tender_object

class EnhancedGeMScraper:
    def __init__(self, timeout=15):
        """Initialize enhanced GeM scraper"""
        self.timeout = timeout
        self.driver = None
        self.wait = None
        
    def create_driver(self):
        """Create Chrome driver for GeM scraping"""
        options = Options()
        options.add_argument('--headless=new')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-images')
        
        # Add realistic user agent
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        try:
            driver = webdriver.Chrome(options=options)
            driver.set_page_load_timeout(self.timeout)
            driver.implicitly_wait(5)
            return driver
        except Exception as e:
            print(f"❌ Error creating driver: {str(e)}")
            return None
    
    def scrape_gem_comprehensive(self, url_config):
        """Comprehensive GeM scraping with pagination and search"""
        print(f"💎 Enhanced GeM Scraping: {url_config['name']}")
        
        self.driver = self.create_driver()
        if not self.driver:
            return []
        
        self.wait = WebDriverWait(self.driver, self.timeout)
        all_tenders = []
        
        try:
            # Step 1: Access GeM CPPP page
            print(f"🔗 Accessing GeM CPPP...")
            self.driver.get("https://gem.gov.in/cppp")
            time.sleep(5)

            # Step 2: Try direct extraction from current page first
            print("🔍 Extracting tenders from main page...")
            main_page_tenders = self._extract_tenders_from_current_page()
            if main_page_tenders:
                all_tenders.extend(main_page_tenders)
                print(f"✅ Found {len(main_page_tenders)} tenders from main page")

            # Step 3: Try to search for empanelment tenders
            if len(all_tenders) < 10:
                empanelment_tenders = self._search_for_keyword("empanelment")
                if empanelment_tenders:
                    all_tenders.extend(empanelment_tenders)
                    print(f"🎯 Found {len(empanelment_tenders)} empanelment tenders")

            # Step 4: Try alternative search terms
            if len(all_tenders) < 5:
                for keyword in ['architect', 'interior', 'consultant', 'construction']:
                    keyword_tenders = self._search_for_keyword(keyword)
                    if keyword_tenders:
                        all_tenders.extend(keyword_tenders)
                        print(f"🔍 Found {len(keyword_tenders)} '{keyword}' tenders")
                        if len(all_tenders) >= 10:  # Stop if we have enough
                            break

            # Step 5: Try pagination if still not enough results
            if len(all_tenders) < 5:
                paginated_tenders = self._scrape_with_pagination()
                all_tenders.extend(paginated_tenders)
                print(f"📄 Found {len(paginated_tenders)} tenders via pagination")

            # Remove duplicates
            unique_tenders = self._remove_duplicates(all_tenders)

            print(f"🎉 Enhanced GeM scraping completed: {len(unique_tenders)} unique tenders")
            
        except Exception as e:
            print(f"❌ Enhanced GeM scraping error: {str(e)}")
        
        finally:
            if self.driver:
                self.driver.quit()
        
        return unique_tenders
    
    def _search_for_keyword(self, keyword):
        """Search for specific keyword in GeM portal"""
        tenders = []
        
        try:
            print(f"🔍 Searching for '{keyword}' in GeM...")
            
            # Go to main CPPP page
            self.driver.get("https://gem.gov.in/cppp")
            time.sleep(3)
            
            # Look for search inputs
            search_inputs = self.driver.find_elements(By.XPATH, "//input[@type='text']")
            
            for search_input in search_inputs:
                try:
                    # Check if this input is visible and interactable
                    if search_input.is_displayed() and search_input.is_enabled():
                        search_input.clear()
                        search_input.send_keys(keyword)
                        
                        # Look for submit button
                        submit_buttons = self.driver.find_elements(By.XPATH, 
                            "//input[@type='submit'] | //button[@type='submit'] | //button[contains(text(), 'Search')] | //button[contains(text(), 'Go')]")
                        
                        for submit_btn in submit_buttons:
                            if submit_btn.is_displayed() and submit_btn.is_enabled():
                                submit_btn.click()
                                time.sleep(5)
                                
                                # Extract results
                                search_results = self._extract_tenders_from_current_page()
                                if search_results:
                                    tenders.extend(search_results)
                                    print(f"✅ Search for '{keyword}' returned {len(search_results)} results")
                                    return tenders
                                break
                        break
                        
                except Exception as e:
                    print(f"⚠️ Search attempt failed: {str(e)}")
                    continue
            
            print(f"⚠️ Could not perform search for '{keyword}'")
            
        except Exception as e:
            print(f"❌ Search error for '{keyword}': {str(e)}")
        
        return tenders
    
    def _scrape_with_pagination(self):
        """Scrape all pages using pagination"""
        all_tenders = []
        page_num = 1
        max_pages = 10  # Limit to prevent infinite loops
        
        try:
            print(f"📄 Starting pagination scraping...")
            
            while page_num <= max_pages:
                print(f"📄 Processing page {page_num}...")
                
                # Extract tenders from current page
                page_tenders = self._extract_tenders_from_current_page()
                
                if page_tenders:
                    all_tenders.extend(page_tenders)
                    print(f"✅ Page {page_num}: {len(page_tenders)} tenders")
                else:
                    print(f"⚠️ Page {page_num}: No tenders found")
                
                # Try to go to next page
                if not self._go_to_next_page():
                    print(f"🏁 No more pages available after page {page_num}")
                    break
                
                page_num += 1
                time.sleep(2)  # Be respectful to the server
            
            print(f"📄 Pagination complete: {len(all_tenders)} total tenders from {page_num-1} pages")
            
        except Exception as e:
            print(f"❌ Pagination error: {str(e)}")
        
        return all_tenders
    
    def _go_to_next_page(self):
        """Navigate to next page"""
        try:
            # Look for "Next" button or link
            next_elements = self.driver.find_elements(By.XPATH, 
                "//a[contains(text(), 'Next')] | //button[contains(text(), 'Next')] | //a[contains(@title, 'Next')]")
            
            for next_elem in next_elements:
                try:
                    if next_elem.is_displayed() and next_elem.is_enabled():
                        # Check if it's not disabled
                        classes = next_elem.get_attribute('class') or ''
                        if 'disabled' not in classes.lower():
                            next_elem.click()
                            time.sleep(3)
                            return True
                except Exception:
                    continue
            
            # Try numeric pagination (look for page numbers)
            page_links = self.driver.find_elements(By.XPATH, "//a[contains(@href, 'page')] | //a[text()='2'] | //a[text()='3']")
            
            for page_link in page_links:
                try:
                    if page_link.is_displayed() and page_link.is_enabled():
                        page_link.click()
                        time.sleep(3)
                        return True
                except Exception:
                    continue
            
            return False
            
        except Exception as e:
            print(f"⚠️ Next page navigation error: {str(e)}")
            return False
    
    def _extract_tenders_from_current_page(self):
        """Extract tenders from current page"""
        tenders = []

        try:
            # Wait for page to load completely
            import time
            time.sleep(2)

            # Try multiple extraction strategies for GeM portal

            # Strategy 1: Look for tender tables
            tables = self.driver.find_elements(By.TAG_NAME, "table")
            print(f"🔍 Found {len(tables)} tables on page")

            for table in tables:
                try:
                    rows = table.find_elements(By.TAG_NAME, "tr")

                    if len(rows) > 3:  # Table with content
                        print(f"📋 Processing table with {len(rows)} rows")
                        # Skip header row
                        for row in rows[1:]:
                            try:
                                tender = self._extract_tender_from_row(row)
                                if tender:
                                    tenders.append(tender)
                            except Exception as e:
                                continue

                        if tenders:
                            print(f"✅ Found {len(tenders)} tenders in table")
                            break  # Found the main table

                except Exception as e:
                    continue

            # Strategy 2: Look for div-based tender listings
            if not tenders:
                print("🔍 Trying div-based extraction...")
                tender_divs = self.driver.find_elements(By.XPATH,
                    "//div[contains(@class, 'tender') or contains(@class, 'bid') or contains(@class, 'notice')]")

                for div in tender_divs[:10]:  # Limit to 10
                    try:
                        text = div.text.strip()
                        if len(text) > 20 and any(keyword in text.lower() for keyword in ['tender', 'bid', 'procurement']):
                            tender = self._create_basic_tender(text, div)
                            if tender:
                                tenders.append(tender)
                    except Exception:
                        continue

            # Strategy 3: Look for any links with tender-related text
            if not tenders:
                print("🔍 Trying link-based extraction...")
                links = self.driver.find_elements(By.TAG_NAME, "a")

                for link in links[:20]:  # Limit to 20 links
                    try:
                        text = link.text.strip()
                        if len(text) > 15 and any(keyword in text.lower() for keyword in ['tender', 'bid', 'procurement', 'rfp']):
                            tender = self._create_basic_tender(text, link)
                            if tender:
                                tenders.append(tender)
                    except Exception:
                        continue

        except Exception as e:
            print(f"⚠️ Page extraction error: {str(e)}")

        print(f"📊 Total tenders extracted: {len(tenders)}")
        return tenders
    
    def _extract_tender_from_row(self, row):
        """Extract tender data from table row"""
        try:
            cells = row.find_elements(By.TAG_NAME, "td")
            
            if len(cells) < 4:  # Not enough data
                return None
            
            # Extract data from cells
            # GeM table structure: [Closing Date, Opening Date, Published Date, Title, Organization, ...]
            closing_date = cells[0].text.strip() if len(cells) > 0 else ""
            opening_date = cells[1].text.strip() if len(cells) > 1 else ""
            published_date = cells[2].text.strip() if len(cells) > 2 else ""
            title = cells[3].text.strip() if len(cells) > 3 else ""
            organization = cells[4].text.strip() if len(cells) > 4 else ""
            
            # Validate title
            if not title or len(title) < 10:
                return None
            
            # Check if contains target keywords or is relevant
            full_text = f"{title} {organization}"
            if not (contains_target_keywords(full_text) or 
                   any(keyword in full_text.lower() for keyword in ['tender', 'bid', 'procurement', 'rfp'])):
                return None
            
            # Look for download links
            download_links = row.find_elements(By.TAG_NAME, "a")
            tender_link = None
            document_link = None
            
            for link in download_links:
                href = link.get_attribute('href')
                if href:
                    if 'download' in href.lower() or '.pdf' in href.lower():
                        document_link = href
                    else:
                        tender_link = href
            
            # Create tender object
            tender = create_tender_object(
                title=title,
                source_name=f"GeM Portal - {organization[:50]}" if organization else "GeM Portal",
                source_url="https://gem.gov.in/cppp",
                reference_number=self._extract_reference(title),
                closing_date=closing_date,
                published_date=published_date,
                bid_opening_date=opening_date,
                tender_link=tender_link,
                document_link=document_link,
                authority_name=organization,
                description=f"{title} - {organization}"
            )
            
            return tender
            
        except Exception as e:
            print(f"⚠️ Row extraction error: {str(e)}")
            return None
    
    def _extract_reference(self, text):
        """Extract reference number from text"""
        patterns = [
            r'[A-Z]{2,}/[0-9]{4}',
            r'[0-9]{4}/[A-Z]{2,}',
            r'NIT[:\s]*([A-Z0-9/\-]+)',
            r'TENDER[:\s]*([A-Z0-9/\-]+)',
            r'REF[:\s]*([A-Z0-9/\-]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(0)
        
        return None
    
    def _remove_duplicates(self, tenders):
        """Remove duplicate tenders based on title"""
        seen_titles = set()
        unique_tenders = []
        
        for tender in tenders:
            title = tender.get('title', '').strip()
            if title and title not in seen_titles:
                seen_titles.add(title)
                unique_tenders.append(tender)
        
        return unique_tenders

    def _create_basic_tender(self, text, element):
        """Create a basic tender object from text and element"""
        try:
            # Check if text contains target keywords
            if not (contains_target_keywords(text) or
                   any(keyword in text.lower() for keyword in ['tender', 'bid', 'procurement', 'rfp'])):
                return None

            # Get link if available
            tender_link = None
            if element.tag_name == 'a':
                href = element.get_attribute('href')
                if href:
                    tender_link = href

            # Create tender object
            tender = create_tender_object(
                title=text[:200],  # Limit title length
                source_name="GeM Portal - CPPP",
                source_url="https://gem.gov.in/cppp",
                reference_number=self._extract_reference(text),
                tender_link=tender_link,
                authority_name="Government e-Marketplace (GeM)",
                description=text
            )

            return tender

        except Exception as e:
            print(f"⚠️ Basic tender creation error: {str(e)}")
            return None

def test_enhanced_gem_scraper():
    """Test the enhanced GeM scraper"""
    print("🧪 TESTING ENHANCED GeM SCRAPER")
    print("=" * 70)
    
    url_config = {
        "name": "GeM Portal - Enhanced",
        "url": "https://gem.gov.in/cppp"
    }
    
    scraper = EnhancedGeMScraper(timeout=20)
    tenders = scraper.scrape_gem_comprehensive(url_config)
    
    print(f"\n📊 ENHANCED SCRAPER RESULTS: {len(tenders)} tenders found")
    
    if tenders:
        print("\n📋 Sample enhanced results:")
        for i, tender in enumerate(tenders[:5], 1):
            print(f"  {i}. {tender['title'][:80]}...")
            print(f"     Authority: {tender.get('authority_name', 'N/A')}")
            print(f"     Closing: {tender.get('closing_date', 'N/A')}")
            print(f"     Reference: {tender.get('reference_number', 'N/A')}")
            print()
        
        # Check keyword matches
        keyword_matches = sum(1 for t in tenders if contains_target_keywords(t['title']) or contains_target_keywords(t.get('description', '')))
        print(f"🎯 Keyword matches: {keyword_matches}/{len(tenders)} tenders")
        
    else:
        print("⚠️ No tenders found with enhanced scraper")
    
    return tenders

if __name__ == "__main__":
    test_enhanced_gem_scraper()
