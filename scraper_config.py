# Configuration file for the tender scraper
# Add or modify URLs here to scrape different tender websites

# List of URLs to scrape
URLS_TO_SCRAPE = [
    # Government eProcurement Portals
    {
        "url": "https://gem.gov.in/cppp",
        "name": "GeM Portal - CPPP",
        "description": "Government e-Marketplace - Central Public Procurement Portal"
    },

    # Banking Sector Tenders
    {
        "url": "https://www.bankofbaroda.in/tenders/zonal-regional-offices",
        "name": "Bank of Baroda Tenders",
        "description": "Bank of Baroda - Zonal & Regional Office Tenders"
    },
    {
        "url": "https://bankofindia.co.in/tender",
        "name": "Bank of India Tenders",
        "description": "Bank of India - Official Tender Portal"
    },
    {
        "url": "https://bankofmaharashtra.in/tenders",
        "name": "Bank of Maharashtra Tenders",
        "description": "Bank of Maharashtra - Tender Notifications"
    },
    {
        "url": "https://canarabank.com/tenders",
        "name": "Canara Bank Tenders",
        "description": "Canara Bank - Tender & Procurement Portal"
    },
    {
        "url": "https://www.centralbankofindia.co.in/en/active-tender",
        "name": "Central Bank of India Tenders",
        "description": "Central Bank of India - Active Tenders"
    },
    {
        "url": "https://www.indianbank.in/tenders/",
        "name": "Indian Bank Tenders",
        "description": "Indian Bank - Tender Notifications"
    },
    {
        "url": "https://www.iob.in/tenderlist.aspx",
        "name": "Indian Overseas Bank Tenders",
        "description": "Indian Overseas Bank - Tender List"
    },
    {
        "url": "https://www.pnbindia.in/Tender.aspx",
        "name": "Punjab National Bank Tenders",
        "description": "Punjab National Bank - Tender Portal"
    },
    {
        "url": "https://punjabandsindbank.co.in/module/tender-list",
        "name": "Punjab & Sind Bank Tenders",
        "description": "Punjab & Sind Bank - Tender List"
    },
    {
        "url": "https://sbi.co.in/web/sbi-in-the-news/procurement-news",
        "name": "State Bank of India Procurement",
        "description": "State Bank of India - Procurement News & Tenders"
    },
    {
        "url": "https://www.unionbankofindia.co.in/en/common/tenders-empanelment",
        "name": "Union Bank of India Tenders",
        "description": "Union Bank of India - Tenders & Empanelment"
    },
    {
        "url": "https://www.ucobank.com/tenders",
        "name": "UCO Bank Tenders",
        "description": "UCO Bank - Tender Notifications"
    }
]

# Scraper settings
SCRAPER_SETTINGS = {
    "page_load_wait_time": 10,  # Time to wait for page to load (seconds) - optimized for headless
    "initial_load_wait_time": 8,  # Time to wait after navigating to URL
    "output_file": "indian_tenders.json",  # Changed filename for Indian tenders
    "chrome_headless": True,  # Run Chrome in headless mode (no visible browser)
    "max_retries": 3,  # Number of retries for failed requests
    "retry_delay": 3,  # Delay between retries (seconds) - reduced for faster processing
    "deep_scraping": True,  # Enable deep scraping of entire websites
    "max_pages_per_site": 5,  # Maximum pages to scrape per site for deep scraping
    "only_active_tenders": True,  # Filter to show only active/open tenders
}

# Database settings (for db.py)
DATABASE_CONFIG = {
    "host": "localhost",
    "user": "root", 
    "password": "",
    "port": 3306,
    "database": "tenders"
}
