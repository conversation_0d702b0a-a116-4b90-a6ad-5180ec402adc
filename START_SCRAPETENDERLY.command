#!/bin/bash

# ScrapeTenderly Easy Starter
# Double-click this file to start the application

echo "🚀 Starting ScrapeTenderly Tender Scraper"
echo "=========================================="

# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$SCRIPT_DIR"

echo "📁 Working directory: $SCRIPT_DIR"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "❌ Virtual environment not found!"
    echo "🔧 Creating virtual environment..."
    python3 -m venv venv
    
    if [ $? -eq 0 ]; then
        echo "✅ Virtual environment created successfully"
    else
        echo "❌ Failed to create virtual environment"
        echo "Please make sure Python 3 is installed"
        read -p "Press Enter to exit..."
        exit 1
    fi
fi

# Activate virtual environment
echo "🔄 Activating virtual environment..."
source venv/bin/activate

# Check if requirements are installed
if [ ! -f "venv/lib/python*/site-packages/flask" ]; then
    echo "📦 Installing required packages..."
    pip install -r requirements.txt
    
    if [ $? -eq 0 ]; then
        echo "✅ Packages installed successfully"
    else
        echo "❌ Failed to install packages"
        read -p "Press Enter to exit..."
        exit 1
    fi
fi

# Start the application
echo ""
echo "🌟 Starting ScrapeTenderly Application..."
echo "🌐 The app will open at: http://localhost:8080"
echo "⏹️  Press Ctrl+C to stop the application"
echo ""
echo "✨ Features available:"
echo "   • Professional web interface"
echo "   • Multi-portal tender scraping"
echo "   • Portal management system"
echo "   • Real-time search and filtering"
echo "   • Keyword highlighting"
echo ""
echo "🚀 Starting in 3 seconds..."
sleep 3

# Open browser automatically (optional)
sleep 2 && open http://localhost:8080 &

# Start the Flask application
python app.py

# Keep terminal open if there's an error
if [ $? -ne 0 ]; then
    echo ""
    echo "❌ Application stopped with an error"
    read -p "Press Enter to exit..."
fi
