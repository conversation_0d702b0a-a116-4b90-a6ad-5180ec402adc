# ScrapeTenderly - Professional Tender Scraping Application

A comprehensive web application for scraping and managing tender information from multiple Indian government and banking portals.

## 🚀 Features

### 🌐 Web Application
- **Professional Dashboard**: Modern, responsive web interface
- **Real-time Search**: Search tenders with keyword highlighting
- **Portal Management**: Organize and manage tender portals by categories
- **Statistics Dashboard**: Real-time counts and status indicators

### 🔍 Advanced Scraping
- **Multi-portal Support**: 13+ Indian tender portals
- **Intelligent Pagination**: Automatic page navigation
- **Keyword Targeting**: Focus on architecture, interior, construction tenders
- **Enhanced GeM Integration**: Specialized Government e-Marketplace scraping

### 📊 Portal Management
- **Category Organization**: Government, Banking, GeM, Private portals
- **CRUD Operations**: Add, edit, delete, activate/deactivate portals
- **Professional UI**: Card-based layout with 6 cards per row
- **Real-time Validation**: Client and server-side validation

## 🛠️ Quick Installation

### Option 1: Automatic Setup (Recommended)
```bash
git clone <repository-url>
cd ScrapeTenderly
python setup.py
```

### Option 2: Manual Setup
1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ScrapeTenderly
   ```

2. **Set up virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Install ChromeDriver**
   - Download ChromeDriver from https://chromedriver.chromium.org/
   - Add to PATH or place in project directory

## 🚀 Usage

### Quick Start
```bash
python run.py
```

### Manual Start
1. **Activate virtual environment**
   ```bash
   source venv/bin/activate  # On Windows: venv\\Scripts\\activate
   ```

2. **Start the application**
   ```bash
   python app.py
   ```

3. **Access the web interface**
   - Main App: http://localhost:8080
   - Search: http://localhost:8080/search
   - Portal Management: http://localhost:8080/portals

4. **Run scraping**
   - Click "Start Scraping" on the main page
   - Monitor progress in real-time
   - View results in the search page

## 📁 Project Structure

```
ScrapeTenderly/
├── app.py                          # Main Flask application
├── scraper_config.py               # Portal configurations
├── balanced_fast_scraper.py        # Main scraping engine
├── universal_pagination_scraper.py # Pagination handler
├── enhanced_gem_scraper.py         # GeM portal scraper
├── templates/                      # HTML templates
│   ├── base.html
│   ├── index.html
│   ├── search.html
│   ├── portals.html
│   └── status.html
├── static/                         # CSS and JavaScript
├── instance/                       # Database files
└── venv/                          # Virtual environment
```

## 🎯 Supported Portals

### 🏛️ Government Portals
- GeM Portal (Government e-Marketplace)

### 🏦 Banking Portals
- Bank of India, Bank of Baroda, Central Bank of India
- Canara Bank, Indian Bank, State Bank of India
- Punjab National Bank, Union Bank of India
- And more...

### 💎 GeM Portals
- Enhanced GeM scraping with pagination
- Keyword-based search functionality

## 🔧 Configuration

Edit `scraper_config.py` to:
- Add new portal URLs
- Modify scraping parameters
- Update keyword targeting

## 📊 Features

- **Real-time Scraping**: Live progress updates
- **Keyword Highlighting**: Bold blue highlighting of target keywords
- **Professional UI**: Modern, responsive design
- **Database Integration**: SQLite database with full CRUD operations
- **Error Handling**: Robust error handling and recovery
- **Portal Management**: Complete portal organization system

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions, please open an issue in the repository.

---

**ScrapeTenderly** - Professional Tender Scraping Made Easy! 🚀
