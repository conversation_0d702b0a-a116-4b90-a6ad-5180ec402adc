#!/usr/bin/env python3
"""
ScrapeTenderly Run Script
Quick start script for the application
"""

import os
import sys
import subprocess

def start_application():
    """Start the ScrapeTenderly application"""
    print("🚀 STARTING SCRAPETENDERLY")
    print("=" * 40)
    
    # Check if virtual environment exists
    if not os.path.exists("venv"):
        print("❌ Virtual environment not found!")
        print("   Please run: python setup.py")
        return False
    
    # Check if app.py exists
    if not os.path.exists("app.py"):
        print("❌ app.py not found!")
        return False
    
    print("✅ Starting ScrapeTenderly application...")
    print("🌐 Application will be available at: http://localhost:8080")
    print("⏹️  Press Ctrl+C to stop the application")
    print("-" * 40)
    
    try:
        # Start the application
        if sys.platform == "win32":
            subprocess.run("venv\\Scripts\\python app.py", shell=True)
        else:
            subprocess.run("venv/bin/python app.py", shell=True)
    except KeyboardInterrupt:
        print("\n👋 ScrapeTenderly stopped by user")
    except Exception as e:
        print(f"\n❌ Error starting application: {e}")
        return False
    
    return True

if __name__ == "__main__":
    start_application()
