# ScrapeTenderly Requirements for Mac
# Optimized for macOS with Python 3.13 compatibility

# Core Flask Framework
Flask==3.0.0
Flask-SQLAlchemy==3.1.1

# Database
SQLAlchemy==2.0.23

# Web Scraping
selenium==4.16.0
beautifulsoup4==4.12.2
requests==2.31.0

# HTML/XML Parsing (Mac-compatible version)
# Using html5lib instead of lxml for better Mac compatibility
html5lib==1.1
soupsieve==2.5

# Flask Dependencies
Werkzeug==3.0.1
Jinja2==3.1.2
MarkupSafe==2.1.3
click==8.1.7
blinker==1.7.0
itsdangerous==2.1.2

# Additional utilities
urllib3==2.1.0
certifi==2023.11.17
charset-normalizer==3.3.2
idna==3.6
