#!/usr/bin/env python3
"""
Create Desktop Shortcut for ScrapeTenderly
==========================================
This script creates a desktop shortcut for easy access to ScrapeT<PERSON>ly
"""

import os
import sys
from pathlib import Path

def create_desktop_shortcut():
    """Create a desktop shortcut for ScrapeTenderly"""
    
    # Get current directory (where ScrapeTenderly is located)
    app_dir = Path(__file__).parent.absolute()
    
    # Get desktop path
    desktop = Path.home() / "Desktop"
    
    if not desktop.exists():
        print("❌ Desktop folder not found")
        return False
    
    # Create shortcut content
    if sys.platform == "darwin":  # macOS
        shortcut_path = desktop / "ScrapeTenderly.command"
        shortcut_content = f'''#!/bin/bash
cd "{app_dir}"
python3 EASY_START.py
'''
        
        # Write shortcut file
        with open(shortcut_path, 'w') as f:
            f.write(shortcut_content)
        
        # Make executable
        os.chmod(shortcut_path, 0o755)
        
        print(f"✅ Desktop shortcut created: {shortcut_path}")
        print("🖱️  Double-click 'ScrapeTenderly.command' on your Desktop to start the app!")
        
    elif sys.platform == "win32":  # Windows
        try:
            import winshell
            from win32com.client import Dispatch
            
            shortcut_path = desktop / "ScrapeTenderly.lnk"
            target = sys.executable
            arguments = f'"{app_dir / "EASY_START.py"}"'
            working_dir = str(app_dir)
            
            shell = Dispatch('WScript.Shell')
            shortcut = shell.CreateShortCut(str(shortcut_path))
            shortcut.Targetpath = target
            shortcut.Arguments = arguments
            shortcut.WorkingDirectory = working_dir
            shortcut.IconLocation = target
            shortcut.save()
            
            print(f"✅ Desktop shortcut created: {shortcut_path}")
            print("🖱️  Double-click 'ScrapeTenderly.lnk' on your Desktop to start the app!")
            
        except ImportError:
            # Fallback: create batch file
            shortcut_path = desktop / "ScrapeTenderly.bat"
            shortcut_content = f'''@echo off
cd /d "{app_dir}"
python EASY_START.py
pause
'''
            with open(shortcut_path, 'w') as f:
                f.write(shortcut_content)
            
            print(f"✅ Desktop shortcut created: {shortcut_path}")
            print("🖱️  Double-click 'ScrapeTenderly.bat' on your Desktop to start the app!")
            
    else:  # Linux
        shortcut_path = desktop / "ScrapeTenderly.desktop"
        shortcut_content = f'''[Desktop Entry]
Version=1.0
Type=Application
Name=ScrapeTenderly
Comment=Professional Tender Scraping Application
Exec=python3 "{app_dir / "EASY_START.py"}"
Icon=applications-internet
Path={app_dir}
Terminal=true
StartupNotify=false
'''
        
        with open(shortcut_path, 'w') as f:
            f.write(shortcut_content)
        
        # Make executable
        os.chmod(shortcut_path, 0o755)
        
        print(f"✅ Desktop shortcut created: {shortcut_path}")
        print("🖱️  Double-click 'ScrapeTenderly.desktop' on your Desktop to start the app!")
    
    return True

def main():
    print("🚀 ScrapeTenderly Desktop Shortcut Creator")
    print("=" * 50)
    print()
    
    if create_desktop_shortcut():
        print()
        print("🎉 Success! You can now start ScrapeTenderly from your Desktop!")
        print()
        print("📋 You now have multiple ways to start the app:")
        print("   1. 🖱️  Double-click the Desktop shortcut")
        print("   2. 🖱️  Double-click EASY_START.py in this folder")
        print("   3. 🖱️  Double-click START_SCRAPETENDERLY.command")
        print("   4. ⌨️  Run 'python3 EASY_START.py' in Terminal")
        print()
        print("🌐 The app will open at: http://localhost:8080")
    else:
        print("❌ Failed to create desktop shortcut")
    
    input("Press Enter to exit...")

if __name__ == "__main__":
    main()
