#!/usr/bin/env python3
"""
ScrapeTenderly Setup Script
Easy installation and setup for the tender scraping application
"""

import os
import subprocess
import sys

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        print(f"   Error output: {e.stderr}")
        return False

def setup_scrapetenderly():
    """Set up ScrapeTenderly application"""
    print("🚀 SCRAPETENDERLY SETUP")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    
    # Create virtual environment
    if not os.path.exists("venv"):
        if not run_command("python -m venv venv", "Creating virtual environment"):
            return False
    else:
        print("✅ Virtual environment already exists")
    
    # Activate virtual environment and install dependencies
    if sys.platform == "win32":
        activate_cmd = "venv\\Scripts\\activate"
        pip_cmd = "venv\\Scripts\\pip"
    else:
        activate_cmd = "source venv/bin/activate"
        pip_cmd = "venv/bin/pip"
    
    # Install dependencies
    if not run_command(f"{pip_cmd} install -r requirements.txt", "Installing dependencies"):
        return False
    
    # Create instance directory if it doesn't exist
    if not os.path.exists("instance"):
        os.makedirs("instance")
        print("✅ Created instance directory")
    
    print("\n" + "=" * 50)
    print("🎉 SETUP COMPLETE!")
    print("=" * 50)
    
    print("\n🚀 TO START THE APPLICATION:")
    if sys.platform == "win32":
        print("   venv\\Scripts\\activate")
    else:
        print("   source venv/bin/activate")
    print("   python app.py")
    
    print("\n🌐 THEN VISIT:")
    print("   http://localhost:8080")
    
    print("\n📚 FEATURES:")
    print("   • Professional web interface")
    print("   • Multi-portal tender scraping")
    print("   • Portal management system")
    print("   • Real-time search and filtering")
    print("   • Keyword highlighting")
    
    return True

if __name__ == "__main__":
    success = setup_scrapetenderly()
    if not success:
        print("\n❌ Setup failed. Please check the errors above.")
        sys.exit(1)
    else:
        print("\n🎯 ScrapeTenderly is ready to use!")
