# ScrapeTenderly - Professional Tender Scraping Application
# Core Dependencies

# Web Framework
Flask==3.0.0
Flask-SQLAlchemy==3.1.1

# Database
SQLAlchemy==2.0.23

# Web Scraping
selenium==4.16.0
beautifulsoup4==4.12.2
requests==2.31.0

# HTML Parsing
lxml==4.9.3
soupsieve==2.5

# Utilities
Werkzeug==3.0.1
Jinja2==3.1.2
MarkupSafe==2.1.3
click==8.1.7
blinker==1.7.0
itsdangerous==2.1.2

# Optional: For enhanced performance
# mysql-connector-python==8.2.0  # Uncomment if using MySQL
# psycopg2-binary==2.9.9         # Uncomment if using PostgreSQL
