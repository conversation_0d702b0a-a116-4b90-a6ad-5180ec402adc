#!/usr/bin/env python3
"""
Create Mac App Bundle for ScrapeTenderly
Creates a native macOS .app bundle for easy launching
"""

import os
import sys
import shutil
from pathlib import Path

def create_mac_app_bundle():
    """Create a native Mac .app bundle"""
    print("🍎 Creating ScrapeTenderly Mac App Bundle")
    print("=" * 50)
    
    app_dir = Path(__file__).parent
    app_name = "ScrapeTenderly"
    bundle_name = f"{app_name}.app"
    bundle_path = app_dir / bundle_name
    
    try:
        # Remove existing bundle if it exists
        if bundle_path.exists():
            print(f"🗑️  Removing existing {bundle_name}...")
            shutil.rmtree(bundle_path)
        
        # Create app bundle structure
        print(f"📁 Creating {bundle_name} structure...")
        contents_dir = bundle_path / "Contents"
        macos_dir = contents_dir / "MacOS"
        resources_dir = contents_dir / "Resources"
        
        contents_dir.mkdir(parents=True)
        macos_dir.mkdir()
        resources_dir.mkdir()
        
        # Create Info.plist
        print("📄 Creating Info.plist...")
        info_plist = f'''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>{app_name}</string>
    <key>CFBundleIdentifier</key>
    <string>com.scrapetenderly.app</string>
    <key>CFBundleName</key>
    <string>{app_name}</string>
    <key>CFBundleDisplayName</key>
    <string>ScrapeTenderly</string>
    <key>CFBundleVersion</key>
    <string>1.0</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleSignature</key>
    <string>STND</string>
    <key>LSMinimumSystemVersion</key>
    <string>10.15</string>
    <key>NSHighResolutionCapable</key>
    <true/>
    <key>LSUIElement</key>
    <false/>
    <key>CFBundleDocumentTypes</key>
    <array/>
</dict>
</plist>'''
        
        with open(contents_dir / "Info.plist", 'w') as f:
            f.write(info_plist)
        
        # Create executable script
        print("🚀 Creating executable launcher...")
        executable_script = f'''#!/bin/bash

# ScrapeTenderly Mac App Bundle Launcher
cd "$(dirname "$0")/../../../"

# Check if we're in the right directory
if [ ! -f "app.py" ]; then
    osascript -e 'display dialog "ScrapeTenderly files not found. Please ensure the .app is in the same folder as the ScrapeTenderly files." buttons {{"OK"}} default button "OK" with icon caution'
    exit 1
fi

# Launch the main Mac starter
if [ -f "EASY_START_MAC.command" ]; then
    exec ./EASY_START_MAC.command
elif [ -f "ScrapeTenderly_Mac.app.command" ]; then
    exec ./ScrapeTenderly_Mac.app.command
elif [ -f "EASY_START.py" ]; then
    exec python3 EASY_START.py
else
    osascript -e 'display dialog "ScrapeTenderly launcher not found. Please ensure all files are present." buttons {{"OK"}} default button "OK" with icon caution'
    exit 1
fi
'''
        
        executable_path = macos_dir / app_name
        with open(executable_path, 'w') as f:
            f.write(executable_script)
        
        # Make executable
        os.chmod(executable_path, 0o755)
        
        # Create app icon (simple text-based icon)
        print("🎨 Creating app icon...")
        icon_script = '''#!/usr/bin/env python3
import os
from pathlib import Path

# Create a simple icon using iconutil (if available)
# For now, we'll just create a placeholder
print("Icon creation completed")
'''
        
        with open(resources_dir / "create_icon.py", 'w') as f:
            f.write(icon_script)
        
        print(f"✅ {bundle_name} created successfully!")
        print()
        print("🎉 Mac App Bundle Ready!")
        print(f"📁 Location: {bundle_path}")
        print()
        print("🚀 How to use:")
        print(f"   1. Double-click {bundle_name} to launch ScrapeTenderly")
        print("   2. The app will start automatically")
        print("   3. Your browser will open to http://localhost:8080")
        print()
        print("📝 Note: Keep the .app file in the same folder as your ScrapeTenderly files")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating Mac app bundle: {str(e)}")
        return False

def create_desktop_alias():
    """Create a desktop alias to the app bundle"""
    try:
        desktop = Path.home() / "Desktop"
        app_dir = Path(__file__).parent
        bundle_path = app_dir / "ScrapeTenderly.app"
        
        if not bundle_path.exists():
            print("❌ App bundle not found. Create it first.")
            return False
        
        # Create alias using AppleScript
        alias_script = f'''
tell application "Finder"
    make alias file to folder POSIX file "{bundle_path}" at desktop
    set name of result to "ScrapeTenderly"
end tell
'''
        
        os.system(f'osascript -e \'{alias_script}\'')
        print("✅ Desktop alias created!")
        return True
        
    except Exception as e:
        print(f"❌ Error creating desktop alias: {str(e)}")
        return False

def main():
    print("🍎 SCRAPETENDERLY MAC APP CREATOR")
    print("=" * 60)
    print()
    
    # Create app bundle
    if create_mac_app_bundle():
        print()
        
        # Ask about desktop alias
        try:
            response = input("🖥️  Create desktop shortcut? (y/n): ").lower().strip()
            if response in ['y', 'yes']:
                create_desktop_alias()
        except KeyboardInterrupt:
            print("\n👋 Cancelled by user")
        
        print()
        print("🎊 SETUP COMPLETE!")
        print()
        print("📱 You now have multiple ways to start ScrapeTenderly on Mac:")
        print("   1. 🖱️  Double-click ScrapeTenderly.app")
        print("   2. 🖱️  Double-click EASY_START_MAC.command")
        print("   3. 🖱️  Double-click ScrapeTenderly_Mac.app.command")
        print("   4. 🖱️  Double-click EASY_START.py")
        print()
        print("🌐 All methods will open http://localhost:8080 automatically!")
        
    else:
        print("❌ Failed to create Mac app bundle")
    
    print()
    input("Press Enter to exit...")

if __name__ == "__main__":
    main()
