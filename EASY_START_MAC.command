#!/bin/bash

# ScrapeTenderly Easy Starter for Mac
# Double-click this file to start the application on macOS

# Colors for better output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 ScrapeTenderly Easy Starter for Mac${NC}"
echo -e "${BLUE}=========================================${NC}"
echo -e "${CYAN}✨ Professional Tender Scraping Application${NC}"
echo -e "${CYAN}🌐 Web Interface: http://localhost:8080${NC}"
echo -e "${BLUE}=========================================${NC}"
echo

# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$SCRIPT_DIR"

echo -e "${PURPLE}📁 Working directory: $SCRIPT_DIR${NC}"
echo

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ Python 3 is not installed!${NC}"
    echo -e "${YELLOW}📥 Please install Python 3 from: https://www.python.org/downloads/${NC}"
    echo -e "${YELLOW}   Or install via Homebrew: brew install python3${NC}"
    read -p "Press Enter to exit..."
    exit 1
fi

# Check Python version
PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
echo -e "${GREEN}✅ Python $PYTHON_VERSION detected${NC}"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo -e "${YELLOW}🔧 Creating virtual environment...${NC}"
    python3 -m venv venv
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Virtual environment created successfully${NC}"
    else
        echo -e "${RED}❌ Failed to create virtual environment${NC}"
        echo -e "${YELLOW}Please make sure Python 3 is properly installed${NC}"
        read -p "Press Enter to exit..."
        exit 1
    fi
else
    echo -e "${GREEN}✅ Virtual environment found${NC}"
fi

# Activate virtual environment
echo -e "${CYAN}🔄 Activating virtual environment...${NC}"
source venv/bin/activate

# Check if requirements are installed by looking for Flask
FLASK_CHECK=$(python -c "import flask" 2>&1)
if [ $? -ne 0 ]; then
    echo -e "${YELLOW}📦 Installing required packages...${NC}"
    echo -e "${CYAN}   This may take a few minutes on first run...${NC}"

    # Upgrade pip first (important on Mac)
    echo -e "${CYAN}   • Upgrading pip...${NC}"
    pip install --upgrade pip > /dev/null 2>&1

    # Try Mac-specific requirements first
    if [ -f "requirements_mac.txt" ]; then
        echo -e "${CYAN}   • Installing Mac-optimized packages...${NC}"
        pip install -r requirements_mac.txt
        INSTALL_RESULT=$?
    else
        echo -e "${CYAN}   • Installing standard packages...${NC}"
        pip install -r requirements.txt
        INSTALL_RESULT=$?
    fi

    if [ $INSTALL_RESULT -eq 0 ]; then
        echo -e "${GREEN}✅ All packages installed successfully${NC}"
    else
        echo -e "${YELLOW}⚠️  Standard installation failed, trying Mac fallback...${NC}"

        # Fallback: Install core packages individually
        echo -e "${CYAN}   • Installing core packages individually...${NC}"
        pip install Flask==3.0.0 Flask-SQLAlchemy==3.1.1 SQLAlchemy==2.0.23 > /dev/null 2>&1
        pip install selenium==4.16.0 beautifulsoup4==4.12.2 requests==2.31.0 > /dev/null 2>&1
        pip install html5lib==1.1 soupsieve==2.5 > /dev/null 2>&1
        pip install Werkzeug==3.0.1 Jinja2==3.1.2 MarkupSafe==2.1.3 > /dev/null 2>&1
        pip install click==8.1.7 blinker==1.7.0 itsdangerous==2.1.2 > /dev/null 2>&1

        # Test if Flask works
        FLASK_TEST=$(python -c "import flask; print('OK')" 2>&1)
        if [[ "$FLASK_TEST" == "OK" ]]; then
            echo -e "${GREEN}✅ Core packages installed successfully${NC}"
            echo -e "${YELLOW}ℹ️  Note: Using html5lib instead of lxml for better Mac compatibility${NC}"
        else
            echo -e "${RED}❌ Package installation failed${NC}"
            echo -e "${YELLOW}💡 Mac troubleshooting:${NC}"
            echo -e "${CYAN}   • Install Xcode Command Line Tools: xcode-select --install${NC}"
            echo -e "${CYAN}   • Install Homebrew and Python: brew install python3${NC}"
            read -p "Press Enter to exit..."
            exit 1
        fi
    fi
else
    echo -e "${GREEN}✅ Required packages already installed${NC}"
fi

# Check if app.py exists
if [ ! -f "app.py" ]; then
    echo -e "${RED}❌ app.py not found!${NC}"
    echo -e "${YELLOW}Please make sure you're running this from the ScrapeTenderly directory${NC}"
    read -p "Press Enter to exit..."
    exit 1
fi

echo
echo -e "${GREEN}🌟 Starting ScrapeTenderly Application...${NC}"
echo -e "${CYAN}🌐 The app will open at: http://localhost:8080${NC}"
echo -e "${YELLOW}⏹️  Press Ctrl+C to stop the application${NC}"
echo
echo -e "${PURPLE}✨ Features available:${NC}"
echo -e "${CYAN}   • Professional web interface${NC}"
echo -e "${CYAN}   • Multi-portal tender scraping (14 portals)${NC}"
echo -e "${CYAN}   • Portal management system${NC}"
echo -e "${CYAN}   • Real-time search and filtering${NC}"
echo -e "${CYAN}   • Keyword highlighting (your preferences)${NC}"
echo -e "${CYAN}   • GeM portal integration${NC}"
echo
echo -e "${GREEN}🚀 Starting in 3 seconds...${NC}"
sleep 1
echo -e "${YELLOW}3...${NC}"
sleep 1
echo -e "${YELLOW}2...${NC}"
sleep 1
echo -e "${YELLOW}1...${NC}"
sleep 1

# Open browser automatically after a delay (macOS specific)
(sleep 3 && open http://localhost:8080) &

echo
echo -e "${GREEN}🎉 ScrapeTenderly is starting up!${NC}"
echo -e "${CYAN}🌐 Opening browser automatically...${NC}"
echo

# Start the Flask application
python app.py

# Keep terminal open if there's an error
EXIT_CODE=$?
if [ $EXIT_CODE -ne 0 ]; then
    echo
    echo -e "${RED}❌ Application stopped with an error (Exit code: $EXIT_CODE)${NC}"
    echo -e "${YELLOW}💡 Common solutions:${NC}"
    echo -e "${CYAN}   • Check if port 8080 is already in use${NC}"
    echo -e "${CYAN}   • Make sure all files are present${NC}"
    echo -e "${CYAN}   • Try running: python3 app.py manually${NC}"
    echo
    read -p "Press Enter to exit..."
else
    echo
    echo -e "${GREEN}👋 ScrapeTenderly stopped normally${NC}"
    echo -e "${CYAN}Thank you for using ScrapeTenderly!${NC}"
    echo
    read -p "Press Enter to exit..."
fi
