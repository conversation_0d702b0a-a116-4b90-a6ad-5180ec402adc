#!/bin/bash

# ScrapeTenderly Mac App Launcher
# Creates a native Mac app experience

# Set up colors
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Clear screen for clean start
clear

# Mac-style banner
echo -e "${BLUE}╔══════════════════════════════════════════════════════════╗${NC}"
echo -e "${BLUE}║${NC}  ${CYAN}🍎 ScrapeTenderly for Mac${NC}                              ${BLUE}║${NC}"
echo -e "${BLUE}║${NC}  ${PURPLE}Professional Tender Scraping Application${NC}              ${BLUE}║${NC}"
echo -e "${BLUE}╠══════════════════════════════════════════════════════════╣${NC}"
echo -e "${BLUE}║${NC}  ${GREEN}🌐 Web Interface: http://localhost:8080${NC}               ${BLUE}║${NC}"
echo -e "${BLUE}║${NC}  ${YELLOW}⚡ 14 Government Portals • GeM Integration${NC}             ${BLUE}║${NC}"
echo -e "${BLUE}╚══════════════════════════════════════════════════════════╝${NC}"
echo

# Get script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$SCRIPT_DIR"

echo -e "${PURPLE}📁 App Location: ${SCRIPT_DIR##*/}${NC}"
echo

# macOS-specific Python check
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ Python 3 Required${NC}"
    echo
    echo -e "${YELLOW}📥 Install Options:${NC}"
    echo -e "${CYAN}   1. Download from: https://www.python.org/downloads/macos/${NC}"
    echo -e "${CYAN}   2. Install via Homebrew: ${GREEN}brew install python3${NC}"
    echo -e "${CYAN}   3. Install via Xcode Command Line Tools${NC}"
    echo
    
    # Offer to open Python download page
    read -p "🌐 Open Python download page? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        open "https://www.python.org/downloads/macos/"
    fi
    
    read -p "Press Enter to exit..."
    exit 1
fi

# Check Python version with Mac-friendly output
PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
PYTHON_FULL=$(python3 --version)
echo -e "${GREEN}✅ $PYTHON_FULL detected${NC}"

# Check for Xcode Command Line Tools (helpful for some packages)
if ! xcode-select -p &> /dev/null; then
    echo -e "${YELLOW}⚠️  Xcode Command Line Tools not detected${NC}"
    echo -e "${CYAN}   (May be needed for some Python packages)${NC}"
    echo -e "${CYAN}   Install with: ${GREEN}xcode-select --install${NC}"
    echo
fi

# Virtual environment setup with Mac-specific paths
if [ ! -d "venv" ]; then
    echo -e "${YELLOW}🔧 Setting up Python environment...${NC}"
    echo -e "${CYAN}   Creating virtual environment...${NC}"
    
    python3 -m venv venv
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Virtual environment created${NC}"
    else
        echo -e "${RED}❌ Failed to create virtual environment${NC}"
        echo -e "${YELLOW}💡 Try: sudo python3 -m pip install --upgrade pip${NC}"
        read -p "Press Enter to exit..."
        exit 1
    fi
else
    echo -e "${GREEN}✅ Python environment ready${NC}"
fi

# Activate virtual environment
echo -e "${CYAN}🔄 Activating environment...${NC}"
source venv/bin/activate

# Enhanced package installation for Mac
FLASK_CHECK=$(python -c "import flask" 2>&1)
if [ $? -ne 0 ]; then
    echo -e "${YELLOW}📦 Installing application dependencies...${NC}"
    echo -e "${CYAN}   This may take 2-3 minutes on first run...${NC}"
    
    # Show progress
    echo -e "${PURPLE}   Installing packages:${NC}"
    
    # Upgrade pip first (important on Mac)
    echo -e "${CYAN}   • Upgrading pip...${NC}"
    pip install --upgrade pip > /dev/null 2>&1
    
    # Install requirements with progress
    echo -e "${CYAN}   • Installing Flask and dependencies...${NC}"
    pip install -r requirements.txt
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ All dependencies installed successfully${NC}"
    else
        echo -e "${RED}❌ Package installation failed${NC}"
        echo -e "${YELLOW}💡 Common Mac solutions:${NC}"
        echo -e "${CYAN}   • Install Xcode Command Line Tools: xcode-select --install${NC}"
        echo -e "${CYAN}   • Update pip: python3 -m pip install --upgrade pip${NC}"
        echo -e "${CYAN}   • Check internet connection${NC}"
        read -p "Press Enter to exit..."
        exit 1
    fi
else
    echo -e "${GREEN}✅ Dependencies already installed${NC}"
fi

# App file verification
if [ ! -f "app.py" ]; then
    echo -e "${RED}❌ ScrapeTenderly app files not found${NC}"
    echo -e "${YELLOW}Please ensure you're in the correct directory${NC}"
    read -p "Press Enter to exit..."
    exit 1
fi

echo
echo -e "${GREEN}🎉 ScrapeTenderly Ready to Launch!${NC}"
echo
echo -e "${PURPLE}🚀 Application Features:${NC}"
echo -e "${CYAN}   ✨ Professional web interface${NC}"
echo -e "${CYAN}   🏛️  14 Government tender portals${NC}"
echo -e "${CYAN}   💎 GeM portal integration${NC}"
echo -e "${CYAN}   🔍 Smart keyword filtering${NC}"
echo -e "${CYAN}   📊 Real-time search and results${NC}"
echo -e "${CYAN}   🎯 Keyword highlighting${NC}"
echo

# Mac-specific launch sequence
echo -e "${GREEN}🚀 Launching ScrapeTenderly...${NC}"
echo -e "${CYAN}🌐 Web interface will open automatically${NC}"
echo -e "${YELLOW}⏹️  Press Ctrl+C in this window to stop${NC}"
echo

# Countdown with Mac emoji
for i in 3 2 1; do
    echo -e "${YELLOW}   Starting in $i... 🍎${NC}"
    sleep 1
done

echo -e "${GREEN}🎊 Launching now!${NC}"
echo

# Open browser with Mac-specific command and delay
(sleep 4 && open -a "Safari" http://localhost:8080 2>/dev/null || open http://localhost:8080) &

# Start the application
echo -e "${CYAN}📱 ScrapeTenderly is starting up...${NC}"
echo -e "${PURPLE}🌐 Access at: http://localhost:8080${NC}"
echo

# Run the Flask app
python app.py

# Mac-friendly exit handling
EXIT_CODE=$?
echo
if [ $EXIT_CODE -ne 0 ]; then
    echo -e "${RED}❌ Application encountered an error${NC}"
    echo
    echo -e "${YELLOW}🔧 Mac Troubleshooting:${NC}"
    echo -e "${CYAN}   • Check Activity Monitor for port conflicts${NC}"
    echo -e "${CYAN}   • Restart Terminal and try again${NC}"
    echo -e "${CYAN}   • Run: lsof -ti:8080 | xargs kill -9${NC}"
    echo -e "${CYAN}   • Check Console.app for system errors${NC}"
    echo
else
    echo -e "${GREEN}✅ ScrapeTenderly stopped normally${NC}"
    echo -e "${CYAN}🍎 Thank you for using ScrapeTenderly on Mac!${NC}"
    echo
fi

read -p "Press Enter to close this window..."
