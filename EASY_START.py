#!/usr/bin/env python3
"""
ScrapeTenderly Easy Starter
===========================
Double-click this file to start the ScrapeTenderly application easily!

This script will:
1. Check if virtual environment exists (create if needed)
2. Install required packages
3. Start the application
4. Open your browser automatically
"""

import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path

def print_banner():
    print("🚀 ScrapeTenderly Easy Starter")
    print("=" * 50)
    print("✨ Professional Tender Scraping Application")
    print("🌐 Web Interface: http://localhost:8080")
    print("=" * 50)
    print()

def check_python():
    """Check if Python is available"""
    try:
        version = sys.version_info
        if version.major >= 3 and version.minor >= 8:
            print(f"✅ Python {version.major}.{version.minor} detected")
            return True
        else:
            print(f"❌ Python {version.major}.{version.minor} detected - need Python 3.8+")
            return False
    except:
        print("❌ Python not found")
        return False

def setup_venv():
    """Setup virtual environment if it doesn't exist"""
    venv_path = Path("venv")
    
    if not venv_path.exists():
        print("🔧 Creating virtual environment...")
        try:
            subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
            print("✅ Virtual environment created")
        except subprocess.CalledProcessError:
            print("❌ Failed to create virtual environment")
            return False
    else:
        print("✅ Virtual environment found")
    
    return True

def install_requirements():
    """Install required packages"""
    requirements_file = Path("requirements.txt")
    
    if not requirements_file.exists():
        print("❌ requirements.txt not found")
        return False
    
    # Determine pip path
    if sys.platform == "win32":
        pip_path = Path("venv/Scripts/pip")
    else:
        pip_path = Path("venv/bin/pip")
    
    if not pip_path.exists():
        print("❌ pip not found in virtual environment")
        return False
    
    print("📦 Installing required packages...")
    try:
        subprocess.run([str(pip_path), "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True)
        print("✅ Packages installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print("❌ Failed to install packages")
        print(f"Error: {e}")
        return False

def start_application():
    """Start the Flask application"""
    app_file = Path("app.py")
    
    if not app_file.exists():
        print("❌ app.py not found")
        return False
    
    # Determine python path
    if sys.platform == "win32":
        python_path = Path("venv/Scripts/python")
    else:
        python_path = Path("venv/bin/python")
    
    print("🌟 Starting ScrapeTenderly Application...")
    print("🌐 Opening browser at: http://localhost:8080")
    print("⏹️  Press Ctrl+C to stop the application")
    print()
    
    # Open browser after a delay
    def open_browser():
        time.sleep(3)
        try:
            webbrowser.open("http://localhost:8080")
        except:
            pass
    
    import threading
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        subprocess.run([str(python_path), "app.py"], check=True)
    except subprocess.CalledProcessError:
        print("❌ Application failed to start")
        return False
    except KeyboardInterrupt:
        print("\n🛑 Application stopped by user")
        return True
    
    return True

def main():
    """Main function"""
    print_banner()
    
    # Change to script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    print(f"📁 Working directory: {script_dir}")
    print()
    
    # Check Python
    if not check_python():
        input("Press Enter to exit...")
        return
    
    # Setup virtual environment
    if not setup_venv():
        input("Press Enter to exit...")
        return
    
    # Install requirements
    if not install_requirements():
        input("Press Enter to exit...")
        return
    
    print()
    print("🎉 Setup complete! Starting application...")
    print()
    
    # Start application
    start_application()
    
    print()
    print("👋 Thank you for using ScrapeTenderly!")
    input("Press Enter to exit...")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        input("Press Enter to exit...")
