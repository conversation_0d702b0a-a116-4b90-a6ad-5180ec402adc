@echo off
title ScrapeTenderly Easy Starter

echo.
echo 🚀 Starting ScrapeTenderly Tender Scraper
echo ==========================================
echo.

REM Get the directory where this script is located
cd /d "%~dp0"
echo 📁 Working directory: %CD%
echo.

REM Check if virtual environment exists
if not exist "venv" (
    echo ❌ Virtual environment not found!
    echo 🔧 Creating virtual environment...
    python -m venv venv
    
    if errorlevel 1 (
        echo ❌ Failed to create virtual environment
        echo Please make sure Python 3 is installed
        pause
        exit /b 1
    ) else (
        echo ✅ Virtual environment created successfully
    )
)

REM Activate virtual environment
echo 🔄 Activating virtual environment...
call venv\Scripts\activate.bat

REM Check if requirements are installed
if not exist "venv\Lib\site-packages\flask" (
    echo 📦 Installing required packages...
    pip install -r requirements.txt
    
    if errorlevel 1 (
        echo ❌ Failed to install packages
        pause
        exit /b 1
    ) else (
        echo ✅ Packages installed successfully
    )
)

REM Start the application
echo.
echo 🌟 Starting ScrapeTenderly Application...
echo 🌐 The app will open at: http://localhost:8080
echo ⏹️  Press Ctrl+C to stop the application
echo.
echo ✨ Features available:
echo    • Professional web interface
echo    • Multi-portal tender scraping
echo    • Portal management system
echo    • Real-time search and filtering
echo    • Keyword highlighting
echo.
echo 🚀 Starting in 3 seconds...
timeout /t 3 /nobreak >nul

REM Open browser automatically (optional)
start "" http://localhost:8080

REM Start the Flask application
python app.py

REM Keep terminal open if there's an error
if errorlevel 1 (
    echo.
    echo ❌ Application stopped with an error
    pause
)
