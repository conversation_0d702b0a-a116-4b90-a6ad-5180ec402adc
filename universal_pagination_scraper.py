#!/usr/bin/env python3
"""
Universal Pagination Scraper
- Implements pagination for all government tender portals
- Handles different pagination patterns (Next, Page numbers, Load More)
- Comprehensive tender extraction across multiple pages
"""

import time
import re
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from utils import contains_target_keywords, create_tender_object

class UniversalPaginationScraper:
    def __init__(self, timeout=15):
        """Initialize universal pagination scraper"""
        self.timeout = timeout
        self.driver = None
        self.wait = None
        
    def create_driver(self):
        """Create Chrome driver for scraping"""
        options = Options()
        options.add_argument('--headless=new')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-images')
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        try:
            driver = webdriver.Chrome(options=options)
            driver.set_page_load_timeout(self.timeout)
            driver.implicitly_wait(5)
            return driver
        except Exception as e:
            print(f"❌ Error creating driver: {str(e)}")
            return None
    
    def scrape_with_pagination(self, url_config, max_pages=10):
        """Scrape portal with comprehensive pagination"""
        print(f"📄 Universal Pagination Scraping: {url_config['name']}")
        
        self.driver = self.create_driver()
        if not self.driver:
            return []
        
        self.wait = WebDriverWait(self.driver, self.timeout)
        all_tenders = []
        
        try:
            # Step 1: Access the portal
            print(f"🔗 Accessing: {url_config['url']}")
            self.driver.get(url_config['url'])
            time.sleep(5)
            
            # Step 2: Try to find and navigate to tender section
            tender_section_found = self._navigate_to_tender_section()
            if tender_section_found:
                print("✅ Found tender section")
            else:
                print("⚠️ Using main page for tender extraction")
            
            # Step 3: Extract tenders with pagination
            page_num = 1
            consecutive_empty_pages = 0
            
            while page_num <= max_pages and consecutive_empty_pages < 3:
                print(f"📄 Processing page {page_num}...")
                
                # Extract tenders from current page
                page_tenders = self._extract_tenders_from_current_page(url_config)
                
                if page_tenders:
                    all_tenders.extend(page_tenders)
                    consecutive_empty_pages = 0
                    print(f"✅ Page {page_num}: {len(page_tenders)} tenders")
                else:
                    consecutive_empty_pages += 1
                    print(f"⚠️ Page {page_num}: No tenders found")
                
                # Try to go to next page
                if page_num < max_pages:
                    if not self._go_to_next_page():
                        print(f"🏁 No more pages available after page {page_num}")
                        break
                
                page_num += 1
                time.sleep(2)  # Be respectful to the server
            
            # Remove duplicates
            unique_tenders = self._remove_duplicates(all_tenders)
            
            print(f"📄 Pagination complete: {len(unique_tenders)} unique tenders from {page_num-1} pages")
            
        except Exception as e:
            print(f"❌ Universal pagination error: {str(e)}")
        
        finally:
            if self.driver:
                self.driver.quit()
        
        return unique_tenders
    
    def _navigate_to_tender_section(self):
        """Try to navigate to tender/procurement section"""
        try:
            # Look for tender-related navigation links
            tender_keywords = ['tender', 'procurement', 'bid', 'rfp', 'quotation', 'निविदा']
            
            for keyword in tender_keywords:
                # Look for navigation links
                nav_links = self.driver.find_elements(By.XPATH, 
                    f"//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{keyword}')]")
                
                for link in nav_links:
                    try:
                        if link.is_displayed() and link.is_enabled():
                            link_text = link.text.strip()
                            if len(link_text) > 3 and len(link_text) < 50:  # Reasonable link text
                                print(f"🔗 Clicking tender link: {link_text}")
                                link.click()
                                time.sleep(3)
                                return True
                    except Exception:
                        continue
            
            return False
            
        except Exception as e:
            print(f"⚠️ Navigation error: {str(e)}")
            return False
    
    def _go_to_next_page(self):
        """Navigate to next page using various pagination patterns"""
        try:
            # Pattern 1: "Next" button or link
            next_patterns = [
                "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'next')]",
                "//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'next')]",
                "//a[contains(@title, 'Next')]",
                "//a[contains(@class, 'next')]",
                "//button[contains(@class, 'next')]"
            ]
            
            for pattern in next_patterns:
                try:
                    next_elements = self.driver.find_elements(By.XPATH, pattern)
                    for next_elem in next_elements:
                        if self._click_if_available(next_elem):
                            return True
                except Exception:
                    continue
            
            # Pattern 2: Numeric pagination (look for next number)
            current_page_num = self._get_current_page_number()
            if current_page_num:
                next_page_num = current_page_num + 1
                page_links = self.driver.find_elements(By.XPATH, f"//a[text()='{next_page_num}']")
                
                for page_link in page_links:
                    if self._click_if_available(page_link):
                        return True
            
            # Pattern 3: "Load More" or "Show More" buttons
            load_more_patterns = [
                "//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'load more')]",
                "//button[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'show more')]",
                "//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'more')]"
            ]
            
            for pattern in load_more_patterns:
                try:
                    load_elements = self.driver.find_elements(By.XPATH, pattern)
                    for load_elem in load_elements:
                        if self._click_if_available(load_elem):
                            time.sleep(3)  # Wait for content to load
                            return True
                except Exception:
                    continue
            
            # Pattern 4: Arrow or symbol-based navigation
            arrow_patterns = [
                "//a[contains(text(), '→')]",
                "//a[contains(text(), '>')]",
                "//a[contains(text(), '»')]",
                "//button[contains(text(), '→')]",
                "//button[contains(text(), '>')]"
            ]
            
            for pattern in arrow_patterns:
                try:
                    arrow_elements = self.driver.find_elements(By.XPATH, pattern)
                    for arrow_elem in arrow_elements:
                        if self._click_if_available(arrow_elem):
                            return True
                except Exception:
                    continue
            
            return False
            
        except Exception as e:
            print(f"⚠️ Next page navigation error: {str(e)}")
            return False
    
    def _click_if_available(self, element):
        """Click element if it's available and not disabled"""
        try:
            if element.is_displayed() and element.is_enabled():
                # Check if it's not disabled
                classes = element.get_attribute('class') or ''
                disabled_attr = element.get_attribute('disabled')
                
                if 'disabled' not in classes.lower() and not disabled_attr:
                    element.click()
                    time.sleep(3)
                    return True
        except Exception:
            pass
        return False
    
    def _get_current_page_number(self):
        """Try to determine current page number"""
        try:
            # Look for active/current page indicators
            current_patterns = [
                "//span[contains(@class, 'current')]",
                "//a[contains(@class, 'active')]",
                "//span[contains(@class, 'active')]",
                "//li[contains(@class, 'active')]//a"
            ]
            
            for pattern in current_patterns:
                try:
                    current_elements = self.driver.find_elements(By.XPATH, pattern)
                    for elem in current_elements:
                        text = elem.text.strip()
                        if text.isdigit():
                            return int(text)
                except Exception:
                    continue
            
            return None
            
        except Exception:
            return None
    
    def _extract_tenders_from_current_page(self, url_config):
        """Extract tenders from current page using multiple strategies"""
        tenders = []
        
        try:
            # Strategy 1: Look for tables with tender data
            tables = self.driver.find_elements(By.TAG_NAME, "table")
            
            for table in tables:
                try:
                    rows = table.find_elements(By.TAG_NAME, "tr")
                    
                    if len(rows) > 3:  # Table with substantial content
                        table_tenders = self._extract_tenders_from_table(table, url_config)
                        if table_tenders:
                            tenders.extend(table_tenders)
                            
                except Exception:
                    continue
            
            # Strategy 2: Look for div-based tender listings
            if len(tenders) < 3:
                tender_divs = self.driver.find_elements(By.XPATH, 
                    "//div[contains(@class, 'tender') or contains(@class, 'bid') or contains(@class, 'procurement')]")
                
                for div in tender_divs:
                    try:
                        div_tender = self._extract_tender_from_div(div, url_config)
                        if div_tender:
                            tenders.append(div_tender)
                    except Exception:
                        continue
            
            # Strategy 3: Look for list items
            if len(tenders) < 3:
                list_items = self.driver.find_elements(By.XPATH, "//li[contains(text(), 'tender') or contains(text(), 'bid')]")
                
                for li in list_items:
                    try:
                        li_tender = self._extract_tender_from_element(li, url_config)
                        if li_tender:
                            tenders.append(li_tender)
                    except Exception:
                        continue
                        
        except Exception as e:
            print(f"⚠️ Page extraction error: {str(e)}")
        
        return tenders
    
    def _extract_tenders_from_table(self, table, url_config):
        """Extract tenders from table structure"""
        tenders = []
        
        try:
            rows = table.find_elements(By.TAG_NAME, "tr")
            
            # Skip header row
            for row in rows[1:]:
                try:
                    tender = self._extract_tender_from_row(row, url_config)
                    if tender:
                        tenders.append(tender)
                except Exception:
                    continue
                    
        except Exception as e:
            print(f"⚠️ Table extraction error: {str(e)}")
        
        return tenders
    
    def _extract_tender_from_row(self, row, url_config):
        """Extract tender data from table row"""
        try:
            cells = row.find_elements(By.TAG_NAME, "td")
            
            if len(cells) < 2:  # Not enough data
                return None
            
            # Extract text from all cells
            cell_texts = [cell.text.strip() for cell in cells]
            combined_text = " ".join(cell_texts)
            
            # Find the longest cell text as potential title
            title = max(cell_texts, key=len) if cell_texts else ""
            
            # Validate title
            if not title or len(title) < 15:
                return None
            
            # Check if contains relevant keywords
            if not (contains_target_keywords(combined_text) or 
                   any(keyword in combined_text.lower() for keyword in ['tender', 'bid', 'procurement', 'rfp'])):
                return None
            
            # Look for dates in cells
            closing_date = self._extract_date_from_cells(cell_texts)
            
            # Look for links
            links = row.find_elements(By.TAG_NAME, "a")
            tender_link = None
            document_link = None
            
            for link in links:
                href = link.get_attribute('href')
                if href:
                    if any(ext in href.lower() for ext in ['.pdf', '.doc', '.docx']):
                        document_link = href
                    else:
                        tender_link = href
            
            # Create tender object
            tender = create_tender_object(
                title=title,
                source_name=url_config['name'],
                source_url=url_config['url'],
                reference_number=self._extract_reference(combined_text),
                closing_date=closing_date,
                tender_link=tender_link,
                document_link=document_link,
                description=combined_text[:200]
            )
            
            return tender
            
        except Exception as e:
            print(f"⚠️ Row extraction error: {str(e)}")
            return None
    
    def _extract_tender_from_div(self, div, url_config):
        """Extract tender from div element"""
        try:
            text = div.text.strip()
            
            if len(text) < 20:
                return None
            
            # Check for relevance
            if not (contains_target_keywords(text) or 
                   any(keyword in text.lower() for keyword in ['tender', 'bid', 'procurement'])):
                return None
            
            # Look for links
            links = div.find_elements(By.TAG_NAME, "a")
            tender_link = links[0].get_attribute('href') if links else None
            
            tender = create_tender_object(
                title=text[:100],
                source_name=url_config['name'],
                source_url=url_config['url'],
                reference_number=self._extract_reference(text),
                tender_link=tender_link,
                description=text
            )
            
            return tender
            
        except Exception:
            return None
    
    def _extract_tender_from_element(self, element, url_config):
        """Extract tender from generic element"""
        try:
            text = element.text.strip()
            
            if len(text) < 15:
                return None
            
            if not contains_target_keywords(text):
                return None
            
            tender = create_tender_object(
                title=text,
                source_name=url_config['name'],
                source_url=url_config['url'],
                description=text
            )
            
            return tender
            
        except Exception:
            return None
    
    def _extract_date_from_cells(self, cell_texts):
        """Extract date from cell texts"""
        date_patterns = [
            r'\d{1,2}[-/]\d{1,2}[-/]\d{4}',
            r'\d{4}[-/]\d{1,2}[-/]\d{1,2}',
            r'\d{1,2}\s+\w+\s+\d{4}'
        ]
        
        for text in cell_texts:
            for pattern in date_patterns:
                match = re.search(pattern, text)
                if match:
                    return match.group(0)
        
        return None
    
    def _extract_reference(self, text):
        """Extract reference number from text"""
        patterns = [
            r'[A-Z]{2,}/[0-9]{4}',
            r'[0-9]{4}/[A-Z]{2,}',
            r'NIT[:\s]*([A-Z0-9/\-]+)',
            r'TENDER[:\s]*([A-Z0-9/\-]+)',
            r'REF[:\s]*([A-Z0-9/\-]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(0)
        
        return None
    
    def _remove_duplicates(self, tenders):
        """Remove duplicate tenders based on title"""
        seen_titles = set()
        unique_tenders = []
        
        for tender in tenders:
            title = tender.get('title', '').strip()
            if title and title not in seen_titles:
                seen_titles.add(title)
                unique_tenders.append(tender)
        
        return unique_tenders
