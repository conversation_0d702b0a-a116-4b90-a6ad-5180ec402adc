#!/usr/bin/env python3
"""
Test Mac Compatibility for ScrapeTenderly
"""

def test_mac_compatibility():
    """Test if all required packages work on Mac"""
    print("🍎 TESTING MAC COMPATIBILITY")
    print("=" * 50)
    
    # Test core imports
    try:
        print("📦 Testing Flask...")
        import flask
        print(f"✅ Flask {flask.__version__}")
        
        print("📦 Testing SQLAlchemy...")
        import sqlalchemy
        print(f"✅ SQLAlchemy {sqlalchemy.__version__}")
        
        print("📦 Testing Selenium...")
        import selenium
        print(f"✅ Selenium {selenium.__version__}")
        
        print("📦 Testing BeautifulSoup...")
        import bs4
        print(f"✅ BeautifulSoup {bs4.__version__}")
        
        print("📦 Testing Requests...")
        import requests
        print(f"✅ Requests {requests.__version__}")
        
        # Test HTML parsing alternatives
        try:
            print("📦 Testing lxml...")
            import lxml
            print(f"✅ lxml {lxml.__version__}")
            html_parser = "lxml"
        except ImportError:
            try:
                print("📦 Testing html5lib...")
                import html5lib
                print(f"✅ html5lib {html5lib.__version__}")
                html_parser = "html5lib"
            except ImportError:
                print("⚠️ Using built-in html.parser")
                html_parser = "html.parser"
        
        print(f"🔧 HTML Parser: {html_parser}")
        
        # Test BeautifulSoup with available parser
        print("🧪 Testing HTML parsing...")
        from bs4 import BeautifulSoup
        test_html = "<html><body><div>Test</div></body></html>"
        soup = BeautifulSoup(test_html, html_parser)
        if soup.find('div'):
            print("✅ HTML parsing working")
        
        # Test Chrome driver creation
        print("🚗 Testing Chrome driver...")
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        options = Options()
        options.add_argument('--headless=new')
        options.add_argument('--no-sandbox')
        
        try:
            driver = webdriver.Chrome(options=options)
            print("✅ Chrome driver created successfully")
            driver.quit()
        except Exception as e:
            print(f"⚠️ Chrome driver issue: {str(e)}")
            print("💡 Install ChromeDriver: brew install chromedriver")
        
        print("\n🎉 MAC COMPATIBILITY TEST PASSED!")
        print("✅ ScrapeTenderly should work perfectly on your Mac!")
        return True
        
    except Exception as e:
        print(f"❌ Compatibility test failed: {str(e)}")
        return False

def test_app_startup():
    """Test if the app can start without errors"""
    print("\n🚀 TESTING APP STARTUP")
    print("=" * 30)
    
    try:
        print("📱 Testing app import...")
        import app
        print("✅ App imports successfully")
        
        print("🗄️ Testing database...")
        with app.app.app_context():
            app.db.create_all()
            print("✅ Database creation works")
        
        print("🎉 APP STARTUP TEST PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ App startup test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🍎 SCRAPETENDERLY MAC COMPATIBILITY CHECK")
    print("=" * 60)
    
    # Test compatibility
    compat_ok = test_mac_compatibility()
    
    # Test app startup
    app_ok = test_app_startup()
    
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS:")
    print(f"✅ Mac Compatibility: {'PASSED' if compat_ok else 'FAILED'}")
    print(f"✅ App Startup: {'PASSED' if app_ok else 'FAILED'}")
    
    if compat_ok and app_ok:
        print("\n🎉 ALL TESTS PASSED!")
        print("🍎 ScrapeTenderly is fully compatible with your Mac!")
        print("🚀 You can now use any of the Mac starter files:")
        print("   • EASY_START_MAC.command")
        print("   • ScrapeTenderly_Mac.app.command")
        print("   • EASY_START.py")
    else:
        print("\n⚠️ Some issues detected. Check the errors above.")
    
    print("=" * 60)
